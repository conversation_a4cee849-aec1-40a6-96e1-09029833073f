<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Admin Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; min-height: 100vh; margin: 0; display: flex; flex-direction: column; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .footer { text-align: center; color: #666; margin-top: auto; padding: 1rem; font-size: 0.9rem; }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
  </style>
</head>
<body>
  <div class="container">
    <div style="text-align: center; margin-bottom: 1rem;">
      <a href="landing.html" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">← Back to Home</a>
      <span style="margin: 0 1rem; color: #ccc;">|</span>
      <a href="#" onclick="logout()" style="color: #e53e3e; text-decoration: none; font-size: 0.9rem;">Logout</a>
    </div>
    <h2>Admin Dashboard</h2>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
      <h3 style="margin-top: 0; color: #495057;">Create New Group Session</h3>
      <label>Class/Subject Name:</label>
      <input type="text" id="className" placeholder="e.g., Math 101, Chemistry Lab, etc." />
      <label>Total Groups:</label>
      <input type="number" id="groupCount" min="1" placeholder="Number of groups to create" />
      <button onclick="setupGroups()">Create Group Session</button>
    </div>

    <div id="sessionInfo" style="display: none; background: #d4edda; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #28a745;">
      <h4 style="margin-top: 0; color: #155724;">Session Created Successfully!</h4>
      <p><strong>Class:</strong> <span id="currentClassName"></span></p>
      <p><strong>Groups:</strong> <span id="currentGroupCount"></span></p>
      <p><strong>Joining Link:</strong></p>
      <div style="background: white; padding: 0.5rem; border-radius: 4px; font-family: monospace; word-break: break-all; margin: 0.5rem 0;">
        <span id="joiningLink"></span>
      </div>
      <button onclick="copyLink()" style="background: #28a745; margin-right: 0.5rem;">Copy Link</button>
      <button onclick="openJoinPage()" style="background: #17a2b8;">Test Join Page</button>
    </div>

    <hr />
    <div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #ffc107;">
      <h4 style="margin: 0 0 0.5rem 0; color: #856404;">📋 Group Management</h4>
      <p style="margin: 0 0 1rem 0; font-size: 0.9rem; color: #856404;">
        <strong>Note:</strong> If students joined from different devices, click "Sync Groups" first to see all members.
      </p>
      <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
        <button onclick="syncGroups()" style="flex: 1; min-width: 120px; background: #ffc107; color: #212529;">🔄 Sync Groups</button>
        <button onclick="revealGroups()" style="flex: 1; min-width: 120px;">👥 Reveal Groups</button>
        <button onclick="exportToExcel()" style="flex: 1; min-width: 120px; background: #28a745;" id="exportBtn" disabled>📊 Export Excel</button>
        <button onclick="printGroups()" style="flex: 1; min-width: 120px; background: #6c757d;" id="printBtn" disabled>🖨️ Print</button>
      </div>
    </div>
    <div id="groupResults"></div>
  </div>

  <div class="footer">
    <p>&copy; 2024 SparkOn-Technologies. All rights reserved.</p>
  </div>

  <script>
    function setupGroups() {
      try {
        const className = document.getElementById("className").value.trim();
        const count = parseInt(document.getElementById("groupCount").value);

        if (!className) {
          alert("Please enter a class/subject name.");
          return;
        }

        if (!count || count < 1) {
          alert("Please enter a valid number of groups.");
          return;
        }

        // Create groups object
        const groups = {};
        for (let i = 1; i <= count; i++) {
          groups[i] = [];
        }

        // Generate session ID
        const sessionId = Date.now().toString(36) + Math.random().toString(36).substr(2);

        // Create session data
        const sessionData = {
          id: sessionId,
          className: className,
          groupCount: count,
          createdAt: new Date().toISOString()
        };

        // Store session data
        localStorage.setItem('currentSession', JSON.stringify(sessionData));

        // Initialize groups for this session
        const sessionKey = 'session_' + sessionId;
        localStorage.setItem(sessionKey, JSON.stringify(groups));

        // Show session info
        document.getElementById("currentClassName").textContent = className;
        document.getElementById("currentGroupCount").textContent = count;

        // Generate joining link with session data encoded
        const sessionInfo = {
          id: sessionId,
          className: className,
          groupCount: count,
          createdAt: sessionData.createdAt
        };

        // Encode session info in URL
        const encodedSession = btoa(JSON.stringify(sessionInfo));
        const baseUrl = window.location.origin + window.location.pathname.replace('admin.html', '');
        const joinUrl = baseUrl + 'index.html?session=' + encodedSession;
        document.getElementById("joiningLink").textContent = joinUrl;

        // Show session info panel
        document.getElementById("sessionInfo").style.display = "block";

        // Clear the form
        document.getElementById("className").value = "";
        document.getElementById("groupCount").value = "";

        alert("Group session created successfully!");

      } catch (error) {
        console.error('Error creating group session:', error);
        alert("Error creating group session. Please try again.");
      }
    }

    function syncGroups() {
      try {
        const sessionDataStr = localStorage.getItem('currentSession');
        if (!sessionDataStr) {
          alert("No active session found. Please create a group session first.");
          return;
        }

        const sessionData = JSON.parse(sessionDataStr);
        if (!sessionData || !sessionData.id) {
          alert("Invalid session data. Please create a new group session.");
          return;
        }

      const message = `To sync groups with student data from other devices:

1. Open the joining link in a new tab/window
2. Check the browser's developer tools (F12) → Console
3. Type: localStorage.getItem('session_${sessionData.id}')
4. Copy the result and paste it below

Or manually enter group data in JSON format:
{"1": ["Student1", "Student2"], "2": ["Student3", "Student4"]}`;

      const groupData = prompt(message);

      if (groupData && groupData.trim()) {
        try {
          const parsedGroups = JSON.parse(groupData);
          const sessionKey = 'session_' + sessionData.id;
          localStorage.setItem(sessionKey, JSON.stringify(parsedGroups));
          alert("Groups synced successfully!");
          revealGroups();
        } catch (e) {
          alert("Invalid JSON format. Please check your input and try again.");
        }
      }

      } catch (error) {
        console.error('Error syncing groups:', error);
        alert("Error syncing group data. Please try again.");
      }
    }

    function revealGroups() {
      try {
        const sessionDataStr = localStorage.getItem('currentSession');
        if (!sessionDataStr) {
          alert("No active session found. Please create a group session first.");
          return;
        }

        const sessionData = JSON.parse(sessionDataStr);
        if (!sessionData || !sessionData.id) {
          alert("Invalid session data. Please create a new group session.");
          return;
        }

        const sessionKey = 'session_' + sessionData.id;
        const groupsStr = localStorage.getItem(sessionKey);
        const groups = groupsStr ? JSON.parse(groupsStr) : {};
        const resultDiv = document.getElementById("groupResults");
        resultDiv.innerHTML = "";

        if (Object.keys(groups).length === 0) {
          resultDiv.innerHTML = "<p style='color: #666; text-align: center;'>No groups found for current session. Try clicking 'Sync Groups' first.</p>";
          return;
        }

      // Add session info header
      const headerDiv = document.createElement("div");
      headerDiv.style.cssText = "background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #28a745;";
      headerDiv.innerHTML = `<h4 style="margin: 0; color: #155724;">Group Results for: ${sessionData.className}</h4>`;
      resultDiv.appendChild(headerDiv);

      // Calculate total students
      let totalStudents = 0;
      for (const members of Object.values(groups)) {
        totalStudents += members.length;
      }

      // Add summary
      const summaryDiv = document.createElement("div");
      summaryDiv.style.cssText = "background: #e3f2fd; padding: 0.8rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;";
      summaryDiv.innerHTML = `<strong>Total Students: ${totalStudents} | Groups: ${Object.keys(groups).length}</strong>`;
      resultDiv.appendChild(summaryDiv);

      // Display each group
      for (const [groupNum, members] of Object.entries(groups)) {
        const groupBlock = document.createElement("div");
        groupBlock.style.cssText = "background: white; border: 1px solid #ddd; border-radius: 6px; padding: 1rem; margin-bottom: 0.8rem;";

        const membersList = members.length > 0 ? members.join("<br>") : "<em style='color: #999;'>No members yet</em>";
        groupBlock.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <strong style="color: #495057;">Group ${groupNum}</strong>
            <span style="background: #007bff; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">${members.length} member${members.length !== 1 ? 's' : ''}</span>
          </div>
          <div style="color: #333;">${membersList}</div>
        `;
        resultDiv.appendChild(groupBlock);
      }

      // Enable export and print buttons
      document.getElementById("exportBtn").disabled = false;
      document.getElementById("printBtn").disabled = false;

      } catch (error) {
        console.error('Error revealing groups:', error);
        alert("Error loading group data. Please try again.");
        const resultDiv = document.getElementById("groupResults");
        if (resultDiv) {
          resultDiv.innerHTML = "<p style='color: #dc3545; text-align: center;'>Error loading group data. Please try again.</p>";
        }
      }
    }

    function copyLink() {
      const linkText = document.getElementById("joiningLink").textContent;
      navigator.clipboard.writeText(linkText).then(() => {
        alert("Link copied to clipboard!");
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = linkText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert("Link copied to clipboard!");
      });
    }

    function openJoinPage() {
      const linkText = document.getElementById("joiningLink").textContent;
      window.open(linkText, '_blank');
    }

    function exportToExcel() {
      try {
        const sessionDataStr = localStorage.getItem('currentSession');
        if (!sessionDataStr) {
          alert("No active session found.");
          return;
        }

        const sessionData = JSON.parse(sessionDataStr);
        if (!sessionData || !sessionData.id) {
          alert("Invalid session data.");
          return;
        }

        const sessionKey = 'session_' + sessionData.id;
        const groupsStr = localStorage.getItem(sessionKey);
        const groups = groupsStr ? JSON.parse(groupsStr) : {};

      if (Object.keys(groups).length === 0) {
        alert("No group data to export.");
        return;
      }

      // Create CSV content
      let csvContent = "Class/Subject,Group Number,Student Name,Total Students in Group\n";

      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.length > 0) {
          members.forEach(member => {
            csvContent += `"${sessionData.className}",${groupNum},"${member}",${members.length}\n`;
          });
        } else {
          csvContent += `"${sessionData.className}",${groupNum},"No members",0\n`;
        }
      }

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `${sessionData.className.replace(/[^a-z0-9]/gi, '_')}_Groups_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert("Group data exported successfully!");

      } catch (error) {
        console.error('Error exporting data:', error);
        alert("Error exporting group data. Please try again.");
      }
    }

    function printGroups() {
      try {
        const sessionDataStr = localStorage.getItem('currentSession');
        if (!sessionDataStr) {
          alert("No active session found.");
          return;
        }

        const sessionData = JSON.parse(sessionDataStr);
        if (!sessionData || !sessionData.id) {
          alert("Invalid session data.");
          return;
        }

        const sessionKey = 'session_' + sessionData.id;
        const groupsStr = localStorage.getItem(sessionKey);
        const groups = groupsStr ? JSON.parse(groupsStr) : {};

      if (Object.keys(groups).length === 0) {
        alert("No group data to print.");
        return;
      }

      // Create print content
      let printContent = `
        <html>
        <head>
          <title>Group List - ${sessionData.className}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .group { margin-bottom: 20px; page-break-inside: avoid; }
            .group-title { background: #f0f0f0; padding: 10px; font-weight: bold; border-left: 4px solid #007bff; }
            .members { padding: 10px; border: 1px solid #ddd; }
            .member { padding: 2px 0; }
            .summary { margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px; }
            @media print { .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Group Assignment Results</h1>
            <h2>${sessionData.className}</h2>
            <p>Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          </div>
      `;

      let totalStudents = 0;
      for (const members of Object.values(groups)) {
        totalStudents += members.length;
      }

      printContent += `<div class="summary"><strong>Total Students: ${totalStudents} | Total Groups: ${Object.keys(groups).length}</strong></div>`;

      for (const [groupNum, members] of Object.entries(groups)) {
        printContent += `
          <div class="group">
            <div class="group-title">Group ${groupNum} (${members.length} member${members.length !== 1 ? 's' : ''})</div>
            <div class="members">
        `;

        if (members.length > 0) {
          members.forEach(member => {
            printContent += `<div class="member">• ${member}</div>`;
          });
        } else {
          printContent += `<div class="member" style="color: #999; font-style: italic;">No members assigned</div>`;
        }

        printContent += `</div></div>`;
      }

      printContent += `
          <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9rem;">
            <p>&copy; 2024 SparkOn-Technologies. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;

      // Open print window
      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();

      } catch (error) {
        console.error('Error printing groups:', error);
        alert("Error printing group data. Please try again.");
      }
    }

    function logout() {
      localStorage.removeItem('currentAdmin');
      window.location.href = "landing.html";
    }

    // Check admin authentication
    function checkAuth() {
      try {
        const currentAdmin = localStorage.getItem('currentAdmin');
        if (!currentAdmin) {
          alert('Please login as admin first.');
          window.location.href = 'landing.html';
          return false;
        }
        return true;
      } catch (error) {
        console.error('Error checking authentication:', error);
        alert('Authentication error. Please login again.');
        window.location.href = 'landing.html';
        return false;
      }
    }

    // Load existing session on page load
    window.onload = function() {
      try {
        // Check authentication first
        if (!checkAuth()) return;

        const sessionDataStr = localStorage.getItem('currentSession');
        if (sessionDataStr) {
          const sessionData = JSON.parse(sessionDataStr);

          if (sessionData && sessionData.className && sessionData.groupCount) {
            document.getElementById("currentClassName").textContent = sessionData.className;
            document.getElementById("currentGroupCount").textContent = sessionData.groupCount;

            // Recreate the encoded session URL
            const sessionInfo = {
              id: sessionData.id || Date.now().toString(36),
              className: sessionData.className,
              groupCount: sessionData.groupCount,
              createdAt: sessionData.createdAt || new Date().toISOString()
            };

            const encodedSession = btoa(JSON.stringify(sessionInfo));
            const baseUrl = window.location.origin + window.location.pathname.replace('admin.html', '');
            const joinUrl = baseUrl + 'index.html?session=' + encodedSession;
            document.getElementById("joiningLink").textContent = joinUrl;

            document.getElementById("sessionInfo").style.display = "block";
          }
        }
      } catch (error) {
        console.error('Error loading session data:', error);
        // Clear corrupted session data
        localStorage.removeItem('currentSession');
      }
    }
  </script>
</body>
</html>
