<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Admin Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Admin Dashboard</h2>
    <label>Total Groups:</label>
    <input type="number" id="groupCount" min="1" />
    <button onclick="setupGroups()">Create Group Session</button>
    <hr />
    <button onclick="revealGroups()">Reveal All Group Members</button>
    <div id="groupResults"></div>
  </div>

  <script>
    function setupGroups() {
      const count = parseInt(document.getElementById("groupCount").value);
      if (!count || count < 1) {
        alert("Enter a valid number of groups.");
        return;
      }
      const groups = {};
      for (let i = 1; i <= count; i++) {
        groups[i] = [];
      }
      localStorage.setItem('groups', JSON.stringify(groups));
      alert("Groups initialized: " + count);
    }

    function revealGroups() {
      const groups = JSON.parse(localStorage.getItem('groups')) || {};
      const resultDiv = document.getElementById("groupResults");
      resultDiv.innerHTML = "";

      for (const [groupNum, members] of Object.entries(groups)) {
        const groupBlock = document.createElement("div");
        groupBlock.innerHTML = `<strong>Group ${groupNum}:</strong><br>${members.join("<br>") || "No members"}`;
        resultDiv.appendChild(groupBlock);
        resultDiv.appendChild(document.createElement("hr"));
      }
    }
  </script>
</body>
</html>
