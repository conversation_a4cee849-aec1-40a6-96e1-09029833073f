<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Admin Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; min-height: 100vh; margin: 0; display: flex; flex-direction: column; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .footer { text-align: center; color: #666; margin-top: auto; padding: 1rem; font-size: 0.9rem; }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
  </style>
</head>
<body>
  <div class="container">
    <div style="text-align: center; margin-bottom: 1rem;">
      <a href="landing.html" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">← Back to Home</a>
      <span style="margin: 0 1rem; color: #ccc;">|</span>
      <a href="#" onclick="logout()" style="color: #e53e3e; text-decoration: none; font-size: 0.9rem;">Logout</a>
    </div>
    <h2>Admin Dashboard</h2>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
      <h3 style="margin-top: 0; color: #495057;">Create New Group Session</h3>
      <label>Class/Subject Name:</label>
      <input type="text" id="className" placeholder="e.g., Math 101, Chemistry Lab, etc." />
      <label>Total Groups:</label>
      <input type="number" id="groupCount" min="1" placeholder="Number of groups to create" />
      <button onclick="setupGroups()">Create Group Session</button>
    </div>

    <div id="sessionInfo" style="display: none; background: #d4edda; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #28a745;">
      <h4 style="margin-top: 0; color: #155724;">Session Created Successfully!</h4>
      <p><strong>Class:</strong> <span id="currentClassName"></span></p>
      <p><strong>Groups:</strong> <span id="currentGroupCount"></span></p>
      <p><strong>Joining Link:</strong></p>
      <div style="background: white; padding: 0.5rem; border-radius: 4px; font-family: monospace; word-break: break-all; margin: 0.5rem 0;">
        <span id="joiningLink"></span>
      </div>
      <button onclick="copyLink()" style="background: #28a745; margin-right: 0.5rem;">Copy Link</button>
      <button onclick="openJoinPage()" style="background: #17a2b8;">Test Join Page</button>
    </div>

    <hr />
    <button onclick="revealGroups()">Reveal All Group Members</button>
    <div id="groupResults"></div>
  </div>

  <div class="footer">
    <p>&copy; 2024 SparkOn-Technologies. All rights reserved.</p>
  </div>

  <script>
    function setupGroups() {
      const className = document.getElementById("className").value.trim();
      const count = parseInt(document.getElementById("groupCount").value);

      if (!className) {
        alert("Please enter a class/subject name.");
        return;
      }

      if (!count || count < 1) {
        alert("Please enter a valid number of groups.");
        return;
      }

      // Create groups object
      const groups = {};
      for (let i = 1; i <= count; i++) {
        groups[i] = [];
      }

      // Generate session ID
      const sessionId = Date.now().toString(36) + Math.random().toString(36).substr(2);

      // Create session data
      const sessionData = {
        id: sessionId,
        className: className,
        groupCount: count,
        createdAt: new Date().toISOString()
      };

      // Store session data
      localStorage.setItem('currentSession', JSON.stringify(sessionData));

      // Initialize groups for this session
      const sessionKey = 'session_' + sessionId;
      localStorage.setItem(sessionKey, JSON.stringify(groups));

      // Show session info
      document.getElementById("currentClassName").textContent = className;
      document.getElementById("currentGroupCount").textContent = count;

      // Generate joining link with session data encoded
      const sessionInfo = {
        id: sessionId,
        className: className,
        groupCount: count,
        createdAt: sessionData.createdAt
      };

      // Encode session info in URL
      const encodedSession = btoa(JSON.stringify(sessionInfo));
      const baseUrl = window.location.origin + window.location.pathname.replace('admin.html', '');
      const joinUrl = baseUrl + 'index.html?session=' + encodedSession;
      document.getElementById("joiningLink").textContent = joinUrl;

      // Show session info panel
      document.getElementById("sessionInfo").style.display = "block";

      alert("Group session created successfully!");
    }

    function revealGroups() {
      const sessionData = JSON.parse(localStorage.getItem('currentSession'));
      if (!sessionData) {
        alert("No active session found. Please create a group session first.");
        return;
      }

      const sessionKey = 'session_' + sessionData.id;
      const groups = JSON.parse(localStorage.getItem(sessionKey)) || {};
      const resultDiv = document.getElementById("groupResults");
      resultDiv.innerHTML = "";

      if (Object.keys(groups).length === 0) {
        resultDiv.innerHTML = "<p style='color: #666; text-align: center;'>No groups found for current session.</p>";
        return;
      }

      // Add session info header
      const headerDiv = document.createElement("div");
      headerDiv.style.cssText = "background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #28a745;";
      headerDiv.innerHTML = `<h4 style="margin: 0; color: #155724;">Group Results for: ${sessionData.className}</h4>`;
      resultDiv.appendChild(headerDiv);

      // Calculate total students
      let totalStudents = 0;
      for (const members of Object.values(groups)) {
        totalStudents += members.length;
      }

      // Add summary
      const summaryDiv = document.createElement("div");
      summaryDiv.style.cssText = "background: #e3f2fd; padding: 0.8rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;";
      summaryDiv.innerHTML = `<strong>Total Students: ${totalStudents} | Groups: ${Object.keys(groups).length}</strong>`;
      resultDiv.appendChild(summaryDiv);

      // Display each group
      for (const [groupNum, members] of Object.entries(groups)) {
        const groupBlock = document.createElement("div");
        groupBlock.style.cssText = "background: white; border: 1px solid #ddd; border-radius: 6px; padding: 1rem; margin-bottom: 0.8rem;";

        const membersList = members.length > 0 ? members.join("<br>") : "<em style='color: #999;'>No members yet</em>";
        groupBlock.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <strong style="color: #495057;">Group ${groupNum}</strong>
            <span style="background: #007bff; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">${members.length} member${members.length !== 1 ? 's' : ''}</span>
          </div>
          <div style="color: #333;">${membersList}</div>
        `;
        resultDiv.appendChild(groupBlock);
      }
    }

    function copyLink() {
      const linkText = document.getElementById("joiningLink").textContent;
      navigator.clipboard.writeText(linkText).then(() => {
        alert("Link copied to clipboard!");
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = linkText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert("Link copied to clipboard!");
      });
    }

    function openJoinPage() {
      const linkText = document.getElementById("joiningLink").textContent;
      window.open(linkText, '_blank');
    }

    function logout() {
      localStorage.removeItem('currentAdmin');
      window.location.href = "landing.html";
    }

    // Load existing session on page load
    window.onload = function() {
      const sessionData = JSON.parse(localStorage.getItem('currentSession'));
      if (sessionData) {
        document.getElementById("currentClassName").textContent = sessionData.className;
        document.getElementById("currentGroupCount").textContent = sessionData.groupCount;

        // Recreate the encoded session URL
        const sessionInfo = {
          id: sessionData.id || Date.now().toString(36),
          className: sessionData.className,
          groupCount: sessionData.groupCount,
          createdAt: sessionData.createdAt
        };
        const encodedSession = btoa(JSON.stringify(sessionInfo));
        const baseUrl = window.location.origin + window.location.pathname.replace('admin.html', '');
        const joinUrl = baseUrl + 'index.html?session=' + encodedSession;
        document.getElementById("joiningLink").textContent = joinUrl;

        document.getElementById("sessionInfo").style.display = "block";
      }
    }
  </script>
</body>
</html>
