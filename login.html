<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Admin Login</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; }
    .container { max-width: 400px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
    .error { color: red; margin-top: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Admin Login</h2>
    <div id="loginForm">
      <label>Username:</label>
      <input type="text" id="username" />
      <label>Password:</label>
      <input type="password" id="password" />
      <button onclick="login()">Login</button>
      <p id="errorMessage" class="error"></p>
      <hr>
      <h3>New Admin?</h3>
      <button onclick="showSignup()">Sign Up</button>
    </div>
    
    <div id="signupForm" style="display: none;">
      <h2>Admin Sign Up</h2>
      <label>Create Username:</label>
      <input type="text" id="newUsername" />
      <label>Create Password:</label>
      <input type="password" id="newPassword" />
      <button onclick="signup()">Create Account</button>
      <p id="signupMessage"></p>
      <button onclick="showLogin()">Back to Login</button>
    </div>
  </div>

  <script>
    function login() {
      const username = document.getElementById("username").value;
      const password = document.getElementById("password").value;
      
      const admins = JSON.parse(localStorage.getItem('admins')) || {};
      
      if (admins[username] && admins[username] === password) {
        localStorage.setItem('currentAdmin', username);
        window.location.href = "admin.html";
      } else {
        document.getElementById("errorMessage").textContent = "Invalid username or password";
      }
    }
    
    function signup() {
      const username = document.getElementById("newUsername").value;
      const password = document.getElementById("newPassword").value;
      
      if (!username || !password) {
        document.getElementById("signupMessage").textContent = "Please enter both username and password";
        document.getElementById("signupMessage").style.color = "red";
        return;
      }
      
      const admins = JSON.parse(localStorage.getItem('admins')) || {};
      
      if (admins[username]) {
        document.getElementById("signupMessage").textContent = "Username already exists";
        document.getElementById("signupMessage").style.color = "red";
        return;
      }
      
      admins[username] = password;
      localStorage.setItem('admins', JSON.stringify(admins));
      
      document.getElementById("signupMessage").textContent = "Account created successfully!";
      document.getElementById("signupMessage").style.color = "green";
      
      setTimeout(showLogin, 1500);
    }
    
    function showSignup() {
      document.getElementById("loginForm").style.display = "none";
      document.getElementById("signupForm").style.display = "block";
    }
    
    function showLogin() {
      document.getElementById("loginForm").style.display = "block";
      document.getElementById("signupForm").style.display = "none";
    }
  </script>
</body>
</html>