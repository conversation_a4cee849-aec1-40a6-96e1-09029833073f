<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Group Randomizer - Welcome</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      padding: 2rem; 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      margin: 0;
    }
    
    .header {
      text-align: center;
      color: white;
      margin-bottom: 3rem;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .header p {
      font-size: 1.2rem;
      opacity: 0.9;
    }
    
    .main-container {
      display: flex;
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;
      flex-wrap: wrap;
    }
    
    .section {
      flex: 1;
      min-width: 300px;
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .section h2 {
      color: #333;
      margin-bottom: 1.5rem;
      text-align: center;
      font-size: 1.8rem;
    }
    
    .student-section {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      text-align: center;
    }
    
    .student-section h2 {
      color: white;
    }
    
    .student-section p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .join-button {
      background: white;
      color: #4facfe;
      border: none;
      padding: 15px 30px;
      font-size: 1.2rem;
      font-weight: bold;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }
    
    .join-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    input, button {
      width: 100%;
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      border: 1px solid #ddd;
      font-size: 1rem;
    }

    .password-container {
      position: relative;
      display: flex;
      align-items: center;
    }

    .password-container input {
      padding-right: 45px;
    }

    .eye-icon {
      position: absolute;
      right: 12px;
      cursor: pointer;
      color: #666;
      font-size: 1.2rem;
      user-select: none;
      z-index: 1;
    }

    .eye-icon:hover {
      color: #333;
    }
    
    button {
      background: #667eea;
      color: white;
      border: none;
      cursor: pointer;
      font-weight: bold;
      transition: background 0.3s ease;
    }
    
    button:hover {
      background: #5a67d8;
    }
    
    .error {
      color: #e53e3e;
      margin-top: 10px;
      font-size: 0.9rem;
    }
    
    .success {
      color: #38a169;
      margin-top: 10px;
      font-size: 0.9rem;
    }
    
    .toggle-link {
      color: #667eea;
      cursor: pointer;
      text-decoration: underline;
      font-size: 0.9rem;
      text-align: center;
      display: block;
      margin-top: 1rem;
    }
    
    .toggle-link:hover {
      color: #5a67d8;
    }
    
    hr {
      margin: 1.5rem 0;
      border: none;
      height: 1px;
      background: #eee;
    }
    
    @media (max-width: 768px) {
      .main-container {
        flex-direction: column;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎲 Group Randomizer</h1>
    <p>Organize students into balanced groups effortlessly</p>
  </div>

  <div class="main-container">
    <!-- Student Section -->
    <div class="section student-section">
      <h2>👥 Students</h2>
      <p>Ready to join a group? Click below to be automatically assigned to a balanced group!</p>
      <a href="index.html" class="join-button">Join a Group</a>
    </div>

    <!-- Admin Section -->
    <div class="section">
      <h2>🔐 Admin Access</h2>
      
      <!-- Login Form -->
      <div id="loginForm">
        <label>Username:</label>
        <input type="text" id="username" placeholder="Enter your username" />
        <label>Password:</label>
        <div class="password-container">
          <input type="password" id="password" placeholder="Enter your password" />
          <span class="eye-icon" onclick="togglePassword('password')" id="eyeIcon1">👁️</span>
        </div>
        <button onclick="login()">Login</button>
        <p id="errorMessage" class="error"></p>

        <hr>
        <span class="toggle-link" onclick="showSignup()">New admin? Create an account</span>
      </div>
      
      <!-- Signup Form -->
      <div id="signupForm" style="display: none;">
        <label>Create Username:</label>
        <input type="text" id="newUsername" placeholder="Choose a username" />
        <label>Create Password:</label>
        <div class="password-container">
          <input type="password" id="newPassword" placeholder="Choose a password" />
          <span class="eye-icon" onclick="togglePassword('newPassword')" id="eyeIcon2">👁️</span>
        </div>
        <button onclick="signup()">Create Admin Account</button>
        <p id="signupMessage"></p>

        <hr>
        <span class="toggle-link" onclick="showLogin()">Already have an account? Login</span>
      </div>
    </div>
  </div>

  <script>
    function login() {
      const username = document.getElementById("username").value;
      const password = document.getElementById("password").value;

      if (!username || !password) {
        document.getElementById("errorMessage").textContent = "Please enter both username and password";
        return;
      }

      const admins = JSON.parse(localStorage.getItem('admins')) || {};

      if (admins[username] && admins[username] === password) {
        localStorage.setItem('currentAdmin', username);
        window.location.href = "admin.html";
      } else {
        document.getElementById("errorMessage").textContent = "Invalid username or password";
      }
    }

    function signup() {
      const username = document.getElementById("newUsername").value;
      const password = document.getElementById("newPassword").value;

      if (!username || !password) {
        document.getElementById("signupMessage").textContent = "Please enter both username and password";
        document.getElementById("signupMessage").className = "error";
        return;
      }

      const admins = JSON.parse(localStorage.getItem('admins')) || {};

      if (admins[username]) {
        document.getElementById("signupMessage").textContent = "Username already exists";
        document.getElementById("signupMessage").className = "error";
        return;
      }

      admins[username] = password;
      localStorage.setItem('admins', JSON.stringify(admins));

      document.getElementById("signupMessage").textContent = "Account created successfully! You can now login.";
      document.getElementById("signupMessage").className = "success";

      setTimeout(showLogin, 2000);
    }

    function showSignup() {
      document.getElementById("loginForm").style.display = "none";
      document.getElementById("signupForm").style.display = "block";
      // Clear any previous messages
      document.getElementById("errorMessage").textContent = "";
    }

    function showLogin() {
      document.getElementById("loginForm").style.display = "block";
      document.getElementById("signupForm").style.display = "none";
      // Clear any previous messages
      document.getElementById("signupMessage").textContent = "";
      // Clear form fields
      document.getElementById("newUsername").value = "";
      document.getElementById("newPassword").value = "";
    }

    function togglePassword(inputId) {
      const passwordInput = document.getElementById(inputId);
      const eyeIcon = inputId === 'password' ? document.getElementById('eyeIcon1') : document.getElementById('eyeIcon2');

      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.textContent = '🙈';
      } else {
        passwordInput.type = 'password';
        eyeIcon.textContent = '👁️';
      }
    }
  </script>
</body>
</html>
