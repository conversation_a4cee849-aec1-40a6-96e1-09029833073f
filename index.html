<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Join Group</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; min-height: 100vh; margin: 0; display: flex; flex-direction: column; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .footer { text-align: center; color: #666; margin-top: auto; padding: 1rem; font-size: 0.9rem; }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
    #groupMessage { font-weight: bold; margin-top: 1rem; }
  </style>
</head>
<body>
  <div class="container">
    <div style="text-align: center; margin-bottom: 1rem;">
      <a href="landing.html" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">← Back to Home</a>
    </div>

    <div id="classInfo" style="background: #e3f2fd; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; text-align: center; border-left: 4px solid #2196f3;">
      <h3 style="margin: 0 0 0.5rem 0; color: #1565c0;">Welcome to</h3>
      <h2 id="className" style="margin: 0; color: #0d47a1; font-size: 1.5rem;">Loading...</h2>
    </div>

    <h2>Join a Random Group</h2>
    <label>Your Name:</label>
    <input type="text" id="studentName" placeholder="Enter your full name" />
    <button onclick="joinBalancedGroup()">Join Group</button>
    <p id="groupMessage"></p>
  </div>

  <div class="footer">
    <p>&copy; 2024 SparkOn-Technologies. All rights reserved.</p>
  </div>

  <script>
    let groups = JSON.parse(localStorage.getItem('groups')) || {};
    let currentSession = JSON.parse(localStorage.getItem('currentSession')) || {};

    // Get class name from URL parameter or session data
    function getClassName() {
      const urlParams = new URLSearchParams(window.location.search);
      const classFromUrl = urlParams.get('class');

      if (classFromUrl) {
        return decodeURIComponent(classFromUrl);
      } else if (currentSession.className) {
        return currentSession.className;
      } else {
        return "Group Session";
      }
    }

    // Initialize page
    window.onload = function() {
      const className = getClassName();
      document.getElementById("className").textContent = className;

      // Check if groups are available
      if (Object.keys(groups).length === 0) {
        document.getElementById("groupMessage").innerHTML =
          '<span style="color: #e53e3e;">⚠️ No active group session found. Please contact your instructor for the correct joining link.</span>';
      }
    }

    function joinBalancedGroup() {
      const name = document.getElementById("studentName").value.trim();
      if (!name) {
        alert("Please enter your name.");
        return;
      }

      let minSize = Infinity;
      let selectedGroup = null;

      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.length < minSize) {
          minSize = members.length;
          selectedGroup = groupNum;
        }
      }

      if (!selectedGroup) {
        alert("Groups not set up yet. Contact admin.");
        return;
      }

      groups[selectedGroup].push(name);
      localStorage.setItem('groups', JSON.stringify(groups));

      document.getElementById("groupMessage").textContent = `You have been added to Group ${selectedGroup}. Please wait for admin to reveal members.`;
      document.getElementById("studentName").value = "";
    }
  </script>
</body>
</html>
