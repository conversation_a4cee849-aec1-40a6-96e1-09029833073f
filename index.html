<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Join Group</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 2rem; background: #f4f4f4; }
    .container { max-width: 600px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    input, button { width: 100%; padding: 10px; margin: 10px 0; border-radius: 5px; border: 1px solid #ccc; }
    #groupMessage { font-weight: bold; margin-top: 1rem; }
  </style>
</head>
<body>
  <div class="container">
    <div style="text-align: center; margin-bottom: 1rem;">
      <a href="landing.html" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">← Back to Home</a>
    </div>
    <h2>Join a Random Group</h2>
    <label>Your Name:</label>
    <input type="text" id="studentName" />
    <button onclick="joinBalancedGroup()">Join Group</button>
    <p id="groupMessage"></p>
  </div>

  <script>
    let groups = JSON.parse(localStorage.getItem('groups')) || {};

    function joinBalancedGroup() {
      const name = document.getElementById("studentName").value.trim();
      if (!name) {
        alert("Please enter your name.");
        return;
      }

      let minSize = Infinity;
      let selectedGroup = null;

      for (const [groupNum, members] of Object.entries(groups)) {
        if (members.length < minSize) {
          minSize = members.length;
          selectedGroup = groupNum;
        }
      }

      if (!selectedGroup) {
        alert("Groups not set up yet. Contact admin.");
        return;
      }

      groups[selectedGroup].push(name);
      localStorage.setItem('groups', JSON.stringify(groups));

      document.getElementById("groupMessage").textContent = `You have been added to Group ${selectedGroup}. Please wait for admin to reveal members.`;
      document.getElementById("studentName").value = "";
    }
  </script>
</body>
</html>
